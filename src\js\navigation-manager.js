// navigation-manager.js - Gestionnaire unique de navigation pour éviter les conflits

class NavigationManager {
    constructor() {
        this.isBuilt = false;
        this.currentPage = null;
        this.isBuilding = false;
        this.buildPromise = null;
    }

    /**
     * Construit le menu une seule fois et le maintient
     */
    async ensureNavigation(activePage) {
        // Si déjà en cours de construction, attendre
        if (this.buildPromise) {
            return this.buildPromise;
        }

        // Si déjà construit, juste mettre à jour le lien actif
        if (this.isBuilt && this.currentPage !== activePage) {
            this.updateActiveLink(activePage);
            this.currentPage = activePage;
            await this.updateStockAlertBadge();
            return;
        }

        // Si pas encore construit, le construire
        if (!this.isBuilt) {
            this.buildPromise = this._buildNavigation(activePage);
            await this.buildPromise;
            this.buildPromise = null;
        }
    }

    async _buildNavigation(activePage) {
        if (this.isBuilding) return;
        
        try {
            this.isBuilding = true;
            
            if (!window.api || !window.api.session) {
                console.error("API de session non disponible.");
                return;
            }

            const t = window.i18n ? window.i18n.t : (key) => key;
            const user = await window.api.session.getCurrentUser();
            const navContainer = document.getElementById('main-nav');
            
            if (!navContainer) {
                console.error("Conteneur de navigation non trouvé");
                return;
            }

            // Construction du HTML du menu
            const links = this._getMenuLinks(t);
            let navHTML = '';
            
            if (user && user.role === 'Propriétaire') {
                navHTML += links.dashboard;
            }
            navHTML += links.caisse;
            if (user && user.role === 'Propriétaire') {
                navHTML += links.products + links.price_adjustment + links.stock_adjustment + 
                          links.clients + links.history + links.credits + links.invoices + links.settings;
            } else {
                navHTML += links.seller_history;
            }

            // Insertion directe sans animation
            navContainer.innerHTML = navHTML;
            
            if (navContainer.children.length === 0) {
                console.error("Échec de l'insertion du menu HTML");
                return;
            }

            this.updateActiveLink(activePage);
            await this.updateStockAlertBadge();
            this.isBuilt = true;
            this.currentPage = activePage;

            console.log(`✅ Menu construit avec succès pour: ${activePage}`);
            
        } catch (error) {
            console.error('Erreur lors de la construction du menu:', error);
        } finally {
            this.isBuilding = false;
        }
    }

    updateActiveLink(activePage) {
        try {
            const navContainer = document.getElementById('main-nav');
            if (!navContainer) return;

            // Supprimer l'état actif de tous les liens
            const allLinks = navContainer.querySelectorAll('.nav-link');
            allLinks.forEach(link => {
                link.classList.remove('active-nav-link', 'bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'text-white', 'shadow-lg');
                link.classList.add('text-white');

                const iconContainer = link.querySelector('div');
                const icon = link.querySelector('svg');
                if (iconContainer && icon) {
                    iconContainer.classList.remove('bg-white/20');
                    icon.classList.remove('text-white');
                    // Restaurer les couleurs d'origine selon l'URL
                    this._restoreOriginalColors(link, iconContainer, icon);
                }
            });

            // Activer le lien correspondant à la page actuelle
            const newActive = navContainer.querySelector(`a[href*="${activePage}.html"]`);
            if (newActive) {
                newActive.classList.add('active-nav-link', 'bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'text-white', 'shadow-lg');

                const iconContainer = newActive.querySelector('div');
                const icon = newActive.querySelector('svg');
                if (iconContainer && icon) {
                    iconContainer.classList.add('bg-white/20');
                    icon.classList.add('text-white');
                }
            }
        } catch (error) {
            console.error('Erreur dans updateActiveLink:', error);
        }
    }

    _restoreOriginalColors(link, iconContainer, icon) {
        const href = link.getAttribute('href');
        const colorMap = {
            'index.html': { icon: 'text-blue-300', bg: 'bg-blue-500/20' },
            'caisse.html': { icon: 'text-green-300', bg: 'bg-green-500/20' },
            'products.html': { icon: 'text-purple-300', bg: 'bg-purple-500/20' },
            'price-adjustment.html': { icon: 'text-yellow-300', bg: 'bg-yellow-500/20' },
            'stock-adjustment.html': { icon: 'text-indigo-300', bg: 'bg-indigo-500/20' },
            'clients.html': { icon: 'text-teal-300', bg: 'bg-teal-500/20' },
            'history.html': { icon: 'text-gray-300', bg: 'bg-gray-500/20' },
            'credits.html': { icon: 'text-red-300', bg: 'bg-red-500/20' },
            'invoices.html': { icon: 'text-orange-300', bg: 'bg-orange-500/20' },
            'settings.html': { icon: 'text-pink-300', bg: 'bg-pink-500/20' }
        };

        for (const [page, colors] of Object.entries(colorMap)) {
            if (href && href.includes(page)) {
                icon.classList.add(colors.icon);
                iconContainer.classList.add(colors.bg);
                break;
            }
        }
    }

    _getMenuLinks(t) {
        return {
            dashboard: `
                <a href="index.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-colors duration-300 text-white hover:bg-gradient-to-r hover:from-blue-600 hover:to-blue-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-blue-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-blue-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_dashboard')}</span>
                </a>`,
            caisse: `
                <a href="caisse.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-colors duration-300 text-white hover:bg-gradient-to-r hover:from-green-600 hover:to-green-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-green-500/20 group-hover:bg-white/20 transition-colors duration-300">
                        <svg class="w-5 h-5 text-green-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_cash_register')}</span>
                </a>`,
            products: `
                <a href="products.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-purple-600 hover:to-purple-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-purple-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-purple-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <span class="flex-1">${t('main_menu_products')}</span>
                </a>`,
            price_adjustment: `
                <a href="price-adjustment.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-yellow-600 hover:to-yellow-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-yellow-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-yellow-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_price_adjustment')}</span>
                </a>`,
            stock_adjustment: `
                <a href="stock-adjustment.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-indigo-600 hover:to-indigo-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-indigo-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-indigo-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_stock_adjustment')}</span>
                </a>`,
            clients: `
                <a href="clients.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-teal-600 hover:to-teal-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-teal-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-teal-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_clients')}</span>
                </a>`,
            history: `
                <a href="history.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-gray-600 hover:to-gray-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-gray-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-gray-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_history')}</span>
                </a>`,
            credits: `
                <a href="credits.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-red-600 hover:to-red-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-red-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-red-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_credits')}</span>
                </a>`,
            invoices: `
                <a href="invoices.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-orange-600 hover:to-orange-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-orange-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-orange-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_invoices')}</span>
                </a>`,
            settings: `
                <a href="settings.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-pink-600 hover:to-pink-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-pink-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-pink-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_settings')}</span>
                </a>`,
            seller_history: `
                <a href="history.html" class="nav-link group flex items-center gap-3 py-3 px-4 text-base font-medium rounded-xl transition-all duration-200 text-white hover:bg-gradient-to-r hover:from-gray-600 hover:to-gray-700 hover:text-white hover:shadow-md">
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg bg-gray-500/20 group-hover:bg-white/20 transition-colors duration-200">
                        <svg class="w-5 h-5 text-gray-300 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <span>${t('main_menu_seller_history')}</span>
                </a>`
        };
    }

    /**
     * Met à jour le badge d'alerte de stock bas
     */
    async updateStockAlertBadge() {
        if (!window.api || !window.api.session || !window.api.products) return;
        try {
            const user = await window.api.session.getCurrentUser();
            if (!user || user.role !== 'Propriétaire') return;

            const lowStockProducts = await window.api.products.getLowStock();
            const productLink = document.querySelector('a[href="products.html"]');
            if (!productLink) return;

            let badge = productLink.querySelector('.alert-badge');
            if (lowStockProducts.length > 0) {
                if (!badge) {
                    badge = document.createElement('span');
                    badge.className = 'alert-badge absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold w-5 h-5 rounded-full flex items-center justify-center animate-pulse';

                    // Ajouter le badge au conteneur d'icône pour un positionnement relatif
                    const iconContainer = productLink.querySelector('div');
                    if (iconContainer) {
                        iconContainer.style.position = 'relative';
                        iconContainer.appendChild(badge);
                    }
                }
                badge.textContent = lowStockProducts.length;
            } else {
                if (badge) badge.remove();
            }
        } catch (error) {
            console.error("Impossible de mettre à jour le badge d'alerte:", error);
        }
    }

    /**
     * Réinitialise complètement le gestionnaire
     */
    reset() {
        this.isBuilt = false;
        this.currentPage = null;
        this.isBuilding = false;
        this.buildPromise = null;
    }
}

// Instance globale unique
const navigationManager = new NavigationManager();

// Exposer globalement
window.navigationManager = navigationManager;

// Fonction de compatibilité avec l'ancien système
window.initializePage = async function(activePage) {
    await navigationManager.ensureNavigation(activePage);
};

window.updateActiveLink = function(activePage) {
    navigationManager.updateActiveLink(activePage);
};

window.rebuildMenu = function(activePage) {
    navigationManager.reset();
    return navigationManager.ensureNavigation(activePage);
};

window.updateStockAlertBadge = function() {
    return navigationManager.updateStockAlertBadge();
};
