<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self'">
    <title data-i18n="cash_register_page_title">Caisse - Système de Gestion</title>

    <!-- Pré-chargement des traductions pour éviter le flash -->
    <script src="./js/lang-preloader.js"></script>

    <link href="./css/output.css" rel="stylesheet">
    <style>
        .search-results-container { position: absolute; z-index: 20; width: 100%; max-height: 150px; overflow-y: auto; background-color: white; border: 1px solid #d1d5db; border-radius: 0 0 0.5rem 0.5rem; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1); }
        .dark .search-results-container { background-color: #374151; border-color: #4b5563; }
        .search-result-item { padding: 0.5rem; cursor: pointer; }
        .search-result-item:hover { background-color: #f3f4f6; }
        .dark .search-result-item:hover { background-color: #4b5563; }
        #product-grid, .overflow-y-auto { scrollbar-width: thin; scrollbar-color: #9ca3af #f3f4f6; }
        .dark #product-grid, .dark .overflow-y-auto { scrollbar-color: #4b5563 #1f2937; }

        /* Stabilisation du menu de navigation */
        #main-nav {
            min-height: 200px; /* Hauteur minimale pour éviter le collapse */
            transition: none !important; /* Désactiver toutes les transitions */
            background-color: rgba(31, 41, 55, 0.1); /* Fond léger pour visualiser */
        }

        #main-nav[data-building] {
            opacity: 1 !important; /* Garder visible même pendant la construction */
        }

        /* Indicateur visuel pour le débogage */
        #main-nav:empty::before {
            content: "🔄 Menu en cours de chargement...";
            color: #9ca3af;
            padding: 1rem;
            display: block;
            text-align: center;
            font-style: italic;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex h-screen font-sans">
    <aside class="w-64 bg-gray-800 text-white flex flex-col flex-shrink-0">
        <div class="p-4 text-2xl font-bold border-b border-gray-700">GestionPro</div>
        <nav id="main-nav" class="flex-grow"></nav>
    </aside>

    <main class="flex-1 flex p-4 gap-4 overflow-hidden">
        <div class="w-1/2 flex flex-col gap-4">
            <div class="flex-shrink-0 bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <!-- Scanner Code-Barres -->
                <div class="mb-4">
                    <label for="barcodeInput" class="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" data-i18n="barcode_scanner_label">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5m0 0v5m0 0h5m0 0V4m0 0h5m0 5v5"></path>
                        </svg>
                        Scanner Code-Barres
                    </label>
                    <div class="relative">
                        <input type="text" id="barcodeInput" class="w-full p-3 border-2 border-blue-300 rounded-lg dark:bg-gray-700 dark:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all" data-i18n-placeholder="barcode_scanner_placeholder" placeholder="Scannez ou tapez le code-barres..." autocomplete="off">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <div id="scannerStatus" class="flex items-center gap-2">
                                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-xs text-gray-500 dark:text-gray-400" data-i18n="scanner_ready">Prêt</span>
                            </div>
                        </div>
                    </div>
                    <div id="scannerFeedback" class="mt-2 text-sm hidden">
                        <div class="flex items-center gap-2 p-2 rounded-lg">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span id="scannerMessage"></span>
                        </div>
                    </div>
                </div>

                <!-- Recherche Manuelle -->
                <div>
                    <label for="productSearch" class="block text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="search_product_label">Rechercher un produit</label>
                    <input type="text" id="productSearch" class="mt-1 w-full p-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600" data-i18n-placeholder="search_product_placeholder">
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="categories_label">Catégories</label>
                    <div class="p-1 mt-1 border rounded-lg flex flex-wrap gap-2" id="category-filters"></div>
                </div>
            </div>
            <div class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <div class="p-4 overflow-y-auto h-full" id="product-grid"></div>
            </div>
        </div>

        <div class="w-1/2 flex flex-col gap-4 min-h-0">
            <div id="lastSalePanel" class="hidden bg-yellow-100 dark:bg-yellow-900/50 p-3 rounded-lg shadow flex-shrink-0">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="font-bold text-yellow-800 dark:text-yellow-200"><span data-i18n="last_sale_panel_title">Dernière Vente:</span> <span id="lastSaleId"></span></p>
                        <p class="text-xs text-yellow-700 dark:text-yellow-300"><span data-i18n="time_left_to_edit">Temps restant pour modifier:</span> <span id="countdownSpan" class="font-semibold">5:00</span></p>
                    </div>
                    <button id="editSaleBtn" class="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600" data-i18n="edit_sale_button">Modifier</button>
                </div>
            </div>

            <div id="clientSearchContainer" class="flex-shrink-0 bg-white dark:bg-gray-800 p-4 rounded-lg shadow relative">
                <label for="clientSearchInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="client_label">Client</label>
                <div class="flex items-center gap-4 mt-1">
                    <div class="relative flex-grow">
                        <input type="text" id="clientSearchInput" class="w-full p-2 border rounded-lg dark:bg-gray-700 dark:border-gray-600" autocomplete="off" data-i18n-placeholder="search_client_placeholder">
                        <div id="clientSearchResults" class="search-results-container hidden"></div>
                    </div>
                    <button id="quickAddClientBtn" class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-xl font-bold hover:bg-blue-600 flex-shrink-0" data-i18n-title="quick_add_client_button" title="Ajouter un nouveau client">+</button>
                    <div id="selectedClientContainer" class="flex-shrink-0 text-center bg-blue-50 dark:bg-blue-900/60 p-2 rounded-lg min-w-[150px] transition-colors duration-200">
                        <div class="flex items-center justify-between gap-2">
                            <p id="selectedClientDisplay" class="font-bold text-blue-800 dark:text-blue-200 truncate flex-1" data-i18n="default_client">Client de passage</p>
                            <span id="clientCreditBadge" class="hidden text-xs font-bold px-2 py-1 rounded-full whitespace-nowrap"></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex-1 flex flex-col bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden min-h-0">
                <div class="flex-1 overflow-y-auto p-2">
                    <table class="min-w-full">
                        <thead class="bg-gray-100 dark:bg-gray-700">
                            <tr>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase" data-i18n="product_header">Produit</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase" data-i18n="quantity_header">Qté</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase" data-i18n="unit_price_header">Prix U.</th>
                                <th class="px-2 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase" data-i18n="total_header">Total</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="cart-items"></tbody>
                    </table>
                </div>
                <div class="p-4 border-t dark:border-gray-700 flex-shrink-0">
                    <div class="flex justify-between items-center text-2xl font-bold">
                        <span data-i18n="total_amount">TOTAL</span>
                        <span><span id="total-amount">0.00</span> MAD</span>
                    </div>
                </div>
            </div>

            <div class="flex-shrink-0 bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
                <!-- Étape 1: Boutons principaux -->
                <div id="payment-step-1" class="flex gap-2 mb-4">
                    <button id="validate-payment-btn" class="w-1/2 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 font-bold text-lg transition-colors" data-i18n="validate_payment_button">
                        ✓ Valider Paiement
                    </button>
                    <button id="cancel-sale-btn" class="w-1/2 bg-red-600 text-white py-3 rounded-lg hover:bg-red-700 font-bold text-lg transition-colors" data-i18n="cancel_sale_button">
                        ✗ Annuler la Vente
                    </button>
                </div>

                <!-- Bouton d'impression (affiché après vente réussie) -->
                <div id="print-section" class="hidden mt-4">
                    <button id="print-ticket-btn" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-bold text-lg transition-colors flex items-center justify-center gap-2" data-i18n="print_ticket_button">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        🖨️ Imprimer Ticket
                    </button>
                </div>


                <!-- Étape 2: Choix du type de paiement (masqué par défaut) -->
                <div id="payment-step-2" class="hidden">
                    <div class="mb-3">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" data-i18n="choose_payment_type">Choisissez le type de paiement :</p>
                    </div>
                    <div class="grid grid-cols-1 gap-2 mb-3">
                        <button id="cash-payment-btn" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 font-bold text-lg transition-colors flex items-center justify-center" data-i18n="cash_payment_button">
                            💰 Paiement Comptant
                        </button>
                        <button id="check-payment-btn" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-bold text-lg transition-colors flex items-center justify-center" data-i18n="check_payment_button">
                            📄 Paiement Chèque
                        </button>
                        <button id="credit-payment-btn" class="w-full bg-orange-600 text-white py-3 rounded-lg hover:bg-orange-700 font-bold text-lg transition-colors flex items-center justify-center" data-i18n="credit_payment_button">
                            💳 Crédit / Partiel
                        </button>
                    </div>
                    <button id="back-to-step1-btn" class="w-full bg-gray-500 text-white py-2 rounded-lg hover:bg-gray-600 font-medium transition-colors" data-i18n="back_button">
                        ← Retour
                    </button>
                </div>

                <!-- Étape 3: Détails pour crédit/partiel (masqué par défaut) -->
                <div id="payment-step-3" class="hidden">
                    <div class="mb-3">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" data-i18n="partial_payment_details">Détails du paiement partiel :</p>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-600 dark:text-gray-400" data-i18n="total_amount">Montant Total :</span>
                            <span id="total-display" class="font-bold text-gray-800 dark:text-white">0.00 MAD</span>
                        </div>
                        <div class="mb-3">
                            <label for="amount-paid" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" data-i18n="amount_paid">Montant Payé :</label>
                            <div class="relative">
                                <input type="number" id="amount-paid" class="w-full p-2 border rounded-lg text-lg dark:bg-gray-700 dark:border-gray-600 pr-16" min="0" step="0.01" value="0">
                                <button id="set-total-btn" class="absolute inset-y-0 right-0 px-3 flex items-center bg-gray-200 dark:bg-gray-600 rounded-r-lg font-bold text-sm hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors" data-i18n-title="set_total_amount_button" title="Remplir avec le total">
                                    Total
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400" data-i18n="credit_amount">Crédit :</span>
                            <span id="credit-display" class="font-bold text-orange-600">0.00 MAD</span>
                        </div>
                    </div>

                    <!-- Affichage des informations de crédit et rendu (seulement dans cette étape) -->
                    <div class="text-right mb-3">
                        <p id="credit-info" class="text-lg font-medium text-orange-600 hidden">Crédit: 0.00 MAD</p>
                        <p id="change-info" class="text-lg font-medium text-green-600 hidden">Rendu: 0.00 MAD</p>
                    </div>
                    <div class="flex gap-2">
                        <button id="confirm-partial-btn" class="w-1/2 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 font-bold text-lg transition-colors" data-i18n="confirm_button">
                            ✓ Confirmer
                        </button>
                        <button id="back-to-step2-btn" class="w-1/2 bg-gray-500 text-white py-3 rounded-lg hover:bg-gray-600 font-medium transition-colors" data-i18n="back_button">
                            ← Retour
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div id="addClientModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden items-center justify-center z-40">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-lg z-50">
            <h2 class="text-2xl font-bold mb-6" data-i18n="add_client_modal_title">Ajouter un Nouveau Client</h2>
            <form id="addClientForm">
                <div class="space-y-4">
                    <div>
                        <label for="modal_client_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="client_name_label">Nom complet</label>
                        <input type="text" id="modal_client_name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2" required>
                    </div>
                    <div>
                        <label for="modal_client_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="client_phone_label">Téléphone</label>
                        <input type="tel" id="modal_client_phone" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2">
                    </div>
                    <div>
                        <label for="modal_client_ice" class="block text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="client_ice_label">ICE</label>
                        <input type="text" id="modal_client_ice" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2">
                    </div>
                    <div>
                        <label for="modal_client_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="client_address_label">Adresse</label>
                        <textarea id="modal_client_address" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm dark:bg-gray-700 dark:border-gray-600 p-2"></textarea>
                    </div>
                </div>
                <div class="mt-8 flex justify-end space-x-4">
                    <button type="button" id="cancelAddClientBtn" class="bg-gray-200 dark:bg-gray-600 px-4 py-2 rounded-lg hover:bg-gray-300" data-i18n="cancel_button">Annuler</button>
                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700" data-i18n="save_button">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
    
    <div id="confirmationModal" class="fixed inset-0 bg-gray-800 bg-opacity-60 hidden items-center justify-center z-50">
        </div>

    <!-- Modal de Choix d'Impression -->
    <div id="printModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" style="display: none; align-items: center; justify-content: center;">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white flex items-center gap-2" data-i18n="print_options_title">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        Options d'Impression
                    </h3>
                    <button id="closePrintModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="space-y-4">
                    <p class="text-gray-600 dark:text-gray-400 text-sm" data-i18n="print_options_description">
                        Choisissez comment vous souhaitez imprimer le ticket :
                    </p>

                    <div class="grid grid-cols-1 gap-3">
                        <!-- Option PDF -->
                        <button id="exportPdfBtn" class="flex items-center gap-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all group">
                            <div class="flex-shrink-0">
                                <svg class="w-8 h-8 text-red-600 group-hover:text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div class="text-left">
                                <h4 class="font-semibold text-gray-900 dark:text-white" data-i18n="export_pdf_title">Exporter en PDF</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400" data-i18n="export_pdf_description">Télécharger le ticket au format PDF</p>
                            </div>
                        </button>

                        <!-- Option Impression Directe -->
                        <button id="directPrintBtn" class="flex items-center gap-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all group">
                            <div class="flex-shrink-0">
                                <svg class="w-8 h-8 text-green-600 group-hover:text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                            </div>
                            <div class="text-left">
                                <h4 class="font-semibold text-gray-900 dark:text-white" data-i18n="direct_print_title">Impression Directe</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400" data-i18n="direct_print_description">Imprimer directement sur l'imprimante thermique</p>
                            </div>
                        </button>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button id="cancelPrintBtn" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors" data-i18n="cancel_button">
                        Annuler
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="./js/i18n.js"></script>
    <script src="./js/preloader.js"></script>
    <script src="./js/page-initializer.js"></script>
    <script src="./js/notifications.js"></script>
    <script src="./js/ticket-printer.js"></script>
    <script src="./js/navigation-manager.js"></script>

    <!-- Script de débogage amélioré -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('� Page caisse chargée avec le nouveau gestionnaire de navigation');

            // Vérifier l'état initial du menu
            const navContainer = document.getElementById('main-nav');
            console.log('📊 État initial du menu:', {
                existe: !!navContainer,
                enfants: navContainer ? navContainer.children.length : 0,
                contenu: navContainer ? navContainer.innerHTML.length : 0
            });

            // Observer les changements
            if (navContainer) {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList') {
                            console.log('🔄 Menu modifié:', {
                                ajoutés: mutation.addedNodes.length,
                                supprimés: mutation.removedNodes.length,
                                totalEnfants: navContainer.children.length,
                                timestamp: new Date().toLocaleTimeString()
                            });
                        }
                    });
                });

                observer.observe(navContainer, { childList: true });

                // Vérification périodique
                let checkCount = 0;
                const interval = setInterval(() => {
                    checkCount++;
                    const links = navContainer.querySelectorAll('.nav-link');
                    console.log(`⏰ Vérification ${checkCount}: ${links.length} liens trouvés`);

                    if (checkCount >= 10) {
                        clearInterval(interval);
                        observer.disconnect();
                        console.log('✅ Surveillance terminée');
                    }
                }, 2000);
            }
        });
    </script>

    <script src="./js/caisse.js"></script>
</body>
</html>